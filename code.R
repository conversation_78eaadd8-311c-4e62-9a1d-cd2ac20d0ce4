
# 步骤1: 加载数据
data <- read.table("DaPars1-2result.txt", header = TRUE, sep = "\t", stringsAsFactors = FALSE)
print(paste("原始数据行数:", nrow(data)))

# 步骤2: 检查关键列中的NA值
print("检查NA值:")
print(paste("Predicted_Proximal_APA列NA数量:", sum(is.na(data$Predicted_Proximal_APA))))
print(paste("Loci列NA数量:", sum(is.na(data$Loci))))
print(paste("A_1_long_exp列NA数量:", sum(is.na(data$A_1_long_exp))))
print(paste("A_1_short_exp列NA数量:", sum(is.na(data$A_1_short_exp))))
print(paste("B_1_long_exp列NA数量:", sum(is.na(data$B_1_long_exp))))
print(paste("B_1_short_exp列NA数量:", sum(is.na(data$B_1_short_exp))))
print(paste("A_1_PDUI列NA数量:", sum(is.na(data$A_1_PDUI))))
print(paste("B_1_PDUI列NA数量:", sum(is.na(data$B_1_PDUI))))

# 步骤3: 移除表达量相关列中含有NA的行（清理无效值）
data_clean <- data[!is.na(data$Predicted_Proximal_APA) &
                   !is.na(data$Loci) &
                   !is.na(data$A_1_long_exp) &
                   !is.na(data$A_1_short_exp) &
                   !is.na(data$A_1_PDUI) &
                   !is.na(data$B_1_long_exp) &
                   !is.na(data$B_1_short_exp) &
                   !is.na(data$B_1_PDUI) &
                   !is.na(data$Group_A_Mean_PDUI) &
                   !is.na(data$Group_B_Mean_PDUI), ]
print(paste("清理后数据行数:", nrow(data_clean)))
# 步骤4: 从Loci列提取远端APA位点 (格式: chr:start-end)
loci_parts <- strsplit(data_clean$Loci, ":")
chr_info <- sapply(loci_parts, function(x) x[1])
pos_range <- sapply(loci_parts, function(x) x[2])

# 步骤5: 分割起始-结束位置
pos_parts <- strsplit(pos_range, "-")
loci_start <- as.numeric(sapply(pos_parts, function(x) x[1]))
loci_end <- as.numeric(sapply(pos_parts, function(x) x[2]))

# 步骤6: 计算aUTR大小 (近端APA与远端APA之间的距离)
data_clean$aUTR_size <- abs(data_clean$Predicted_Proximal_APA - loci_end)
print(paste("aUTR大小范围:", min(data_clean$aUTR_size, na.rm = TRUE),
            "到", max(data_clean$aUTR_size, na.rm = TRUE)))
# 步骤7: 计算每行的总表达量（丰度）
data_clean$total_expression <- data_clean$A_1_long_exp + data_clean$A_1_short_exp +
  data_clean$B_1_long_exp + data_clean$B_1_short_exp

# 步骤8: 从Gene列提取基因名称 (格式: transcript|gene|chr|strand)
gene_parts <- strsplit(data_clean$Gene, "\\|")
data_clean$gene_name <- sapply(gene_parts, function(x) x[2])

# 步骤9: 统计每个基因的出现次数
gene_counts <- table(data_clean$gene_name)
print(paste("总基因数:", length(gene_counts)))

# 步骤10: 识别具有多个polyA位点的基因（多行）
multi_polya_genes <- names(gene_counts[gene_counts > 1])
print(paste("具有多个polyA位点的基因数:", length(multi_polya_genes)))

# 步骤11: 仅筛选多polyA基因
multi_data <- data_clean[data_clean$gene_name %in% multi_polya_genes, ]
print(paste("多polyA基因的总行数:", nrow(multi_data)))

# 步骤12: 为每个基因选择总表达量最高的行（丰度最高）
library(dplyr)
selected_data <- multi_data %>%
  group_by(gene_name) %>%
  slice_max(total_expression, n = 1, with_ties = FALSE) %>%
  ungroup()
print(paste("选择后的基因数:", nrow(selected_data)))

# 步骤13: 计算ΔMLS (long isoform vs. short isoform)
# MLS计算公式: MLS = log2(B_exp/A_exp)
# 为避免log2(0)的问题，添加伪计数
pseudocount <- 1

# 计算MLS long isoform = log2(B_1_long_exp/A_1_long_exp)
selected_data$MLS_long <- log2((selected_data$B_1_long_exp + pseudocount) /
                               (selected_data$A_1_long_exp + pseudocount))

# 计算MLS short isoform = log2(B_1_short_exp/A_1_short_exp)
selected_data$MLS_short <- log2((selected_data$B_1_short_exp + pseudocount) /
                                (selected_data$A_1_short_exp + pseudocount))

# 计算ΔMLS = MLS long isoform - MLS short isoform
selected_data$delta_MLS <- selected_data$MLS_long - selected_data$MLS_short

print(paste("MLS long范围:", round(min(selected_data$MLS_long, na.rm = TRUE), 3),
            "到", round(max(selected_data$MLS_long, na.rm = TRUE), 3)))
print(paste("MLS short范围:", round(min(selected_data$MLS_short, na.rm = TRUE), 3),
            "到", round(max(selected_data$MLS_short, na.rm = TRUE), 3)))
print(paste("ΔMLS范围:", round(min(selected_data$delta_MLS, na.rm = TRUE), 3),
            "到", round(max(selected_data$delta_MLS, na.rm = TRUE), 3)))

# 步骤14: 按aUTR大小将基因分成5个范围
# 计算分位数以创建5个相等范围
quantiles <- quantile(selected_data$aUTR_size, probs = seq(0, 1, 0.2), na.rm = TRUE)

# 显示分位数值
print("步骤14: aUTR大小分位数:")
print(quantiles)

# 创建具体数值范围的标签（类似图片中的格式）
q1 <- round(quantiles[2])  # 20%分位数
q2 <- round(quantiles[3])  # 40%分位数
q3 <- round(quantiles[4])  # 60%分位数
q4 <- round(quantiles[5])  # 80%分位数

# 创建标签
labels <- c(
  paste0("<", q1),
  paste0(q1, " - ", q2 - 1),
  paste0(q2, " - ", q3 - 1),
  paste0(q3, " - ", q4 - 1),
  paste0(">", q4 - 1)
)

print("aUTR大小范围标签:")
print(labels)

# 创建大小类别
selected_data$aUTR_category <- cut(selected_data$aUTR_size,
                                   breaks = quantiles,
                                   labels = labels,
                                   include.lowest = TRUE)

# 检查每个类别的基因数量
print("每个aUTR大小类别的基因数量:")
print(table(selected_data$aUTR_category))
# 步骤15: 按aUTR大小类别计算统计值
mls_summary <- selected_data %>%
  group_by(aUTR_category) %>%
  summarise(
    Gene_bin = cur_group_id(),
    aUTR_size_range = first(aUTR_category),
    No_of_genes = n(),
    median_delta_MLS = round(median(delta_MLS, na.rm = TRUE), 3),
    mean_delta_MLS = round(mean(delta_MLS, na.rm = TRUE), 3),
    sd_delta_MLS = round(sd(delta_MLS, na.rm = TRUE), 3),
    se_delta_MLS = round(sd(delta_MLS, na.rm = TRUE) / sqrt(n()), 3)
  ) %>%
  ungroup()

print("步骤15: ΔMLS汇总表（按aUTR大小范围）:")
print(mls_summary)

# 创建类似您图片格式的最终表格
final_summary <- data.frame(
  Gene_bin = 1:5,
  aUTR_size_range_nt = labels,
  No_of_genes = as.numeric(table(selected_data$aUTR_category)),
  ΔMLS_long_vs_short = mls_summary$median_delta_MLS
)

print("步骤15: 最终汇总表（类似图片格式）:")
print(final_summary)

# 保存所有结果
write.table(selected_data, "multi_polya_genes_with_MLS_analysis.txt", sep = "\t", row.names = FALSE)
write.table(final_summary, "aUTR_MLS_summary_table.txt", sep = "\t", row.names = FALSE)
# 步骤16: 进行统计检验（Wilcoxon单样本检验）
wilcox_results <- selected_data %>%
  group_by(aUTR_category) %>%
  summarise(
    Gene_bin = cur_group_id(),
    aUTR_size_range = first(aUTR_category),
    No_of_genes = n(),
    median_delta_MLS = round(median(delta_MLS, na.rm = TRUE), 3),
    mean_delta_MLS = round(mean(delta_MLS, na.rm = TRUE), 3),
    sd_delta_MLS = round(sd(delta_MLS, na.rm = TRUE), 3),
    se_delta_MLS = round(sd(delta_MLS, na.rm = TRUE) / sqrt(n()), 3),
    # Wilcoxon单样本检验（检验中位数是否显著不为0）
    wilcox_p_value = wilcox.test(delta_MLS, mu = 0)$p.value
  ) %>%
  ungroup()

# 添加显著性标记
wilcox_results$significance <- ifelse(wilcox_results$wilcox_p_value < 0.001, "***",
                               ifelse(wilcox_results$wilcox_p_value < 0.01, "**",
                               ifelse(wilcox_results$wilcox_p_value < 0.05, "*", "ns")))

print("步骤16: Wilcoxon检验结果:")
print(wilcox_results)

# 进行bin 1 vs bin 5的比较（如图片所示）
bin1_data <- selected_data[selected_data$aUTR_category == labels[1], ]$delta_MLS
bin5_data <- selected_data[selected_data$aUTR_category == labels[5], ]$delta_MLS

if (length(bin1_data) > 0 && length(bin5_data) > 0) {
  bin1_vs_bin5_test <- wilcox.test(bin1_data, bin5_data)
  print(paste("Bin 1 vs. bin 5: P =",
              format(bin1_vs_bin5_test$p.value, scientific = TRUE, digits = 3)))
}
# 步骤17: 创建类似图片的可视化
library(ggplot2)
library(dplyr)
library(scales)

# 准备绘图数据
plot_data <- wilcox_results %>%
  mutate(
    # 为x轴创建数值型变量
    bin_numeric = Gene_bin,
    # 创建误差棒的上下限
    ymin = median_delta_MLS - se_delta_MLS,
    ymax = median_delta_MLS + se_delta_MLS
  )

# 创建美化的点线图
p1 <- ggplot(plot_data, aes(x = bin_numeric, y = median_delta_MLS)) +
  # 添加水平参考线（y=0）- 放在最底层
  geom_hline(yintercept = 0,
             linetype = "dashed",
             color = "grey60",
             size = 0.6,
             alpha = 0.8) +
  # 添加连接线
  geom_line(size = 1.5,
            color = "black",
            alpha = 0.9) +
  # 添加误差棒
  geom_errorbar(aes(ymin = ymin, ymax = ymax),
                width = 0.12,
                size = 0.9,
                color = "black",
                alpha = 0.8) +
  # 添加数据点
  geom_point(size = 5,
             color = "black",
             fill = "white",
             shape = 21,
             stroke = 1.8) +
  # 设置坐标轴
  scale_x_continuous(
    breaks = 1:5,
    labels = paste("Bin", 1:5),
    limits = c(0.7, 5.3),
    expand = c(0, 0)
  ) +
  scale_y_continuous(
    breaks = seq(-0.15, 0.1, by = 0.05),
    labels = scales::number_format(accuracy = 0.01),
    limits = c(-0.15, 0.1),
    expand = c(0, 0)
  ) +
  # 设置标签
  labs(
    x = "aUTR Size Bin",
    y = expression(paste(Delta, "MLS (long isoform vs. short isoform)")),
    title = "Effect of aUTR Size on Alternative Polyadenylation",
    subtitle = "Membrane Localization Score Differences Across aUTR Size Bins"
  ) +
  # 使用经典主题并自定义
  theme_classic(base_size = 12) +
  theme(
    # 标题设置
    plot.title = element_text(
      size = 16,
      face = "bold",
      hjust = 0.5,
      margin = margin(b = 10)
    ),
    plot.subtitle = element_text(
      size = 12,
      hjust = 0.5,
      color = "grey30",
      margin = margin(b = 20)
    ),
    # 坐标轴标题
    axis.title.x = element_text(
      size = 14,
      face = "bold",
      margin = margin(t = 15)
    ),
    axis.title.y = element_text(
      size = 14,
      face = "bold",
      margin = margin(r = 15)
    ),
    # 坐标轴文本
    axis.text.x = element_text(
      size = 12,
      color = "black",
      margin = margin(t = 5)
    ),
    axis.text.y = element_text(
      size = 12,
      color = "black",
      margin = margin(r = 5)
    ),
    # 坐标轴线和刻度
    axis.line = element_line(
      color = "black",
      size = 1,
      lineend = "round"
    ),
    axis.ticks = element_line(
      color = "black",
      size = 0.8
    ),
    axis.ticks.length = unit(0.2, "cm"),
    # 面板设置
    panel.background = element_rect(fill = "white", color = NA),
    panel.grid.major = element_blank(),
    panel.grid.minor = element_blank(),
    # 图例和边距
    plot.background = element_rect(fill = "white", color = NA),
    plot.margin = margin(20, 20, 20, 20)
  )

# 显示图表
print(p1)

# 创建一个更简洁的版本（类似原图片风格）
p2 <- ggplot(plot_data, aes(x = bin_numeric, y = median_delta_MLS)) +
  # 添加水平参考线（y=0）
  geom_hline(yintercept = 0,
             linetype = "dashed",
             color = "grey50",
             size = 0.5) +
  # 添加连接线
  geom_line(size = 1.2, color = "black") +
  # 添加误差棒
  geom_errorbar(aes(ymin = ymin, ymax = ymax),
                width = 0.1,
                size = 0.8,
                color = "black") +
  # 添加数据点
  geom_point(size = 4,
             color = "black",
             fill = "white",
             shape = 21,
             stroke = 1.5) +
  # 添加显著性标记
  geom_text(aes(label = ifelse(significance != "ns", significance, "")),
            vjust = -1.5,
            hjust = 0.5,
            size = 4,
            fontface = "bold") +
  # 设置坐标轴
  scale_x_continuous(
    breaks = 1:5,
    labels = 1:5,
    limits = c(0.8, 5.2)
  ) +
  scale_y_continuous(
    breaks = seq(-0.15, 0.1, by = 0.05),
    labels = function(x) sprintf("%.2f", x),
    limits = c(-0.15, 0.1),
    expand = c(0.02, 0.02)
  ) +
  # 设置标签
  labs(
    x = "aUTR size bin",
    y = expression(paste(Delta, "MLS, long isoform vs. short isoform"))
  ) +
  # 使用经典主题
  theme_classic(base_size = 12) +
  theme(
    axis.title = element_text(size = 12, color = "black", face = "bold"),
    axis.text = element_text(size = 11, color = "black"),
    axis.line = element_line(color = "black", size = 0.8),
    axis.ticks = element_line(color = "black", size = 0.6),
    panel.background = element_rect(fill = "white"),
    plot.background = element_rect(fill = "white"),
    plot.margin = margin(15, 15, 15, 15)
  )

# 显示简洁版图表
print(p2)

# 创建一个专门优化的版本（根据实际数据范围）
# 首先检查数据范围
y_range <- range(c(plot_data$ymin, plot_data$ymax), na.rm = TRUE)
print(paste("Y轴数据范围:", round(y_range[1], 3), "到", round(y_range[2], 3)))

p3 <- ggplot(plot_data, aes(x = bin_numeric, y = median_delta_MLS)) +
  # 添加水平参考线（y=0）
  geom_hline(yintercept = 0,
             linetype = "dashed",
             color = "grey50",
             size = 0.5) +
  # 添加连接线
  geom_line(size = 1.2, color = "black") +
  # 添加误差棒
  geom_errorbar(aes(ymin = ymin, ymax = ymax),
                width = 0.1,
                size = 0.8,
                color = "black") +
  # 添加数据点
  geom_point(size = 4,
             color = "black",
             fill = "white",
             shape = 21,
             stroke = 1.5) +
  # 添加显著性标记
  geom_text(aes(label = ifelse(significance != "ns", significance, "")),
            vjust = -2,
            hjust = 0.5,
            size = 4,
            fontface = "bold") +
  # 设置坐标轴 - 根据实际数据范围调整
  scale_x_continuous(
    breaks = 1:5,
    labels = 1:5,
    limits = c(0.8, 5.2)
  ) +
  scale_y_continuous(
    breaks = seq(floor(y_range[1]*20)/20, ceiling(y_range[2]*20)/20, by = 0.05),
    labels = function(x) sprintf("%.2f", x),
    limits = c(floor(y_range[1]*20)/20 - 0.02, ceiling(y_range[2]*20)/20 + 0.02)
  ) +
  # 设置标签
  labs(
    x = "aUTR size bin",
    y = expression(paste(Delta, "MLS, long isoform vs. short isoform"))
  ) +
  # 使用经典主题
  theme_classic(base_size = 12) +
  theme(
    axis.title = element_text(size = 12, color = "black", face = "bold"),
    axis.text = element_text(size = 11, color = "black"),
    axis.line = element_line(color = "black", size = 0.8),
    axis.ticks = element_line(color = "black", size = 0.6),
    panel.background = element_rect(fill = "white"),
    plot.background = element_rect(fill = "white"),
    plot.margin = margin(15, 15, 15, 15)
  )

# 显示优化版图表
print(p3)

# 保存三个版本的图表
ggsave("Figure5_aUTR_MLS_analysis_detailed.png", plot = p1,
       width = 10, height = 6, dpi = 300, bg = "white")
ggsave("Figure5_aUTR_MLS_analysis_detailed.pdf", plot = p1,
       width = 10, height = 6, bg = "white")

ggsave("Figure5_aUTR_MLS_analysis_simple.png", plot = p2,
       width = 6, height = 4, dpi = 300, bg = "white")
ggsave("Figure5_aUTR_MLS_analysis_simple.pdf", plot = p2,
       width = 6, height = 4, bg = "white")

ggsave("Figure5_aUTR_MLS_analysis_optimized.png", plot = p3,
       width = 6, height = 4, dpi = 300, bg = "white")
ggsave("Figure5_aUTR_MLS_analysis_optimized.pdf", plot = p3,
       width = 6, height = 4, bg = "white")
# 步骤18: 创建最终的详细结果表
final_detailed_table <- wilcox_results %>%
  select(Gene_bin, aUTR_size_range, No_of_genes,
         median_delta_MLS, se_delta_MLS, wilcox_p_value, significance) %>%
  mutate(
    wilcox_p_value = format(wilcox_p_value, scientific = TRUE, digits = 3)
  )

print("步骤18: 最终详细结果表:")
print(final_detailed_table)

# 创建包含aUTR范围信息的表格（类似图片右侧表格）
summary_table_for_figure <- data.frame(
  Gene_bin = 1:5,
  aUTR_size_range_nt = labels,
  No_of_genes = as.numeric(table(selected_data$aUTR_category))
)

print("类似图片格式的汇总表:")
print(summary_table_for_figure)

# 保存所有结果
write.table(selected_data, "multi_polya_genes_complete_analysis.txt",
            sep = "\t", row.names = FALSE)
write.table(final_detailed_table, "aUTR_MLS_summary_with_stats.txt",
            sep = "\t", row.names = FALSE)
write.table(summary_table_for_figure, "aUTR_size_bins_summary.txt",
            sep = "\t", row.names = FALSE)
write.table(wilcox_results, "wilcoxon_test_results.txt",
            sep = "\t", row.names = FALSE)

print("分析完成！所有结果已保存。")
