
# Load the data
data <- read.table("DaPars1-2result.txt", header = TRUE, sep = "\t", stringsAsFactors = FALSE)

# 检查关键列中的NA值
sum(is.na(data$Predicted_Proximal_APA))
sum(is.na(data$Loci))

# 移除关键列中含有NA的行
data_clean <- data[!is.na(data$Predicted_Proximal_APA) & !is.na(data$Loci), ]
# 检查关键列中的NA值
sum(is.na(data$Predicted_Proximal_APA))
sum(is.na(data$Loci))

# 移除关键列中含有NA的行
data_clean <- data[!is.na(data$Predicted_Proximal_APA) & !is.na(data$Loci), ]
# 从Loci列提取起始和结束位置 (格式: chr:start-end)
loci_parts <- strsplit(data_clean$Loci, ":")
chr_info <- sapply(loci_parts, function(x) x[1])
pos_range <- sapply(loci_parts, function(x) x[2])

# 分割起始-结束位置
pos_parts <- strsplit(pos_range, "-")
loci_start <- as.numeric(sapply(pos_parts, function(x) x[1]))
loci_end <- as.numeric(sapply(pos_parts, function(x) x[2]))

# 计算aUTR大小 (近端APA与远端APA之间的距离)
data_clean$aUTR_size <- abs(data_clean$Predicted_Proximal_APA - loci_end)
# 计算每行的总表达量
data_clean$total_expression <- data_clean$A_1_long_exp + data_clean$A_1_short_exp + 
                               data_clean$B_1_long_exp + data_clean$B_1_short_exp
# 从Gene列提取基因名称 (格式: transcript|gene|chr|strand)
gene_parts <- strsplit(data_clean$Gene, "\\|")
data_clean$gene_name <- sapply(gene_parts, function(x) x[2])

# 统计每个基因的出现次数
gene_counts <- table(data_clean$gene_name)

# 识别具有多个polyA位点的基因（多行）
multi_polya_genes <- names(gene_counts[gene_counts > 1])
length(multi_polya_genes)
# 仅筛选多polyA基因
multi_data <- data_clean[data_clean$gene_name %in% multi_polya_genes, ]

# 为每个基因选择总表达量最高的行
library(dplyr)
selected_data <- multi_data %>%
  group_by(gene_name) %>%
  slice_max(total_expression, n = 1, with_ties = FALSE) %>%
  ungroup()

# 计算每个样本的MLS long isoform和MLS short isoform的log2值
# 假设A_1和B_1代表两个样本/条件

# 为避免log2(0)的问题，添加伪计数
pseudocount <- 1

# 计算A组的MLS差值 (long - short)
selected_data$A_MLS_long <- log2(selected_data$A_1_long_exp + pseudocount)
selected_data$A_MLS_short <- log2(selected_data$A_1_short_exp + pseudocount)
selected_data$A_MLS_diff <- selected_data$A_MLS_long - selected_data$A_MLS_short

# 计算B组的MLS差值 (long - short)
selected_data$B_MLS_long <- log2(selected_data$B_1_long_exp + pseudocount)
selected_data$B_MLS_short <- log2(selected_data$B_1_short_exp + pseudocount)
selected_data$B_MLS_diff <- selected_data$B_MLS_long - selected_data$B_MLS_short

# 计算每个基因的平均MLS差值
selected_data$mean_MLS_diff <- (selected_data$A_MLS_diff + selected_data$B_MLS_diff) / 2

# 计算分位数以创建5个相等范围
quantiles <- quantile(selected_data$aUTR_size, probs = seq(0, 1, 0.2), na.rm = TRUE)

# 显示分位数值
print("aUTR大小分位数:")
print(quantiles)

# 创建具体数值范围的标签
q1 <- round(quantiles[2])  # 20%分位数
q2 <- round(quantiles[3])  # 40%分位数  
q3 <- round(quantiles[4])  # 60%分位数
q4 <- round(quantiles[5])  # 80%分位数

# 创建标签
labels <- c(
  paste0("<", q1),                    
  paste0(q1, "-", q2-1),             
  paste0(q2, "-", q3-1),             
  paste0(q3, "-", q4-1),             
  paste0(">", q4-1)                  
)

# 创建大小类别
selected_data$aUTR_category <- cut(selected_data$aUTR_size, 
                                   breaks = quantiles, 
                                   labels = labels,
                                   include.lowest = TRUE)
# 按aUTR大小类别计算MLS差值的中位数
mls_summary <- selected_data %>%
  group_by(aUTR_category) %>%
  summarise(
    Gene_bin = cur_group_id(),
    aUTR_size_range = first(aUTR_category),
    No_of_genes = n(),
    median_MLS_diff = round(median(mean_MLS_diff, na.rm = TRUE), 3),
    mean_MLS_diff = round(mean(mean_MLS_diff, na.rm = TRUE), 3),
    sd_MLS_diff = round(sd(mean_MLS_diff, na.rm = TRUE), 3)
  ) %>%
  ungroup()

print("MLS差值汇总表（按aUTR大小范围）:")
print(mls_summary)


# 创建类似您图片格式的最终表格
final_summary <- data.frame(
  Gene_bin = 1:5,
  aUTR_size_range_nt = labels,
  No_of_genes = as.numeric(table(selected_data$aUTR_category)),
  Median_MLS_diff = mls_summary$median_MLS_diff
)

print("最终汇总表:")
print(final_summary)

# 保存所有结果
write.table(selected_data, "multi_polya_genes_with_MLS_analysis.txt", sep = "\t", row.names = FALSE)
write.table(final_summary, "aUTR_MLS_summary_table.txt", sep = "\t", row.names = FALSE)
# 为每个aUTR大小类别进行Wilcoxon单样本检验（检验MLS差值是否显著不为0）
wilcox_results <- selected_data %>%
  group_by(aUTR_category) %>%
  summarise(
    Gene_bin = cur_group_id(),
    aUTR_size_range = first(aUTR_category),
    No_of_genes = n(),
    median_MLS_diff = round(median(mean_MLS_diff, na.rm = TRUE), 3),
    mean_MLS_diff = round(mean(mean_MLS_diff, na.rm = TRUE), 3),
    sd_MLS_diff = round(sd(mean_MLS_diff, na.rm = TRUE), 3),
    se_MLS_diff = round(sd(mean_MLS_diff, na.rm = TRUE) / sqrt(n()), 3),
    # Wilcoxon单样本检验（检验中位数是否显著不为0）
    wilcox_p_value = wilcox.test(mean_MLS_diff, mu = 0)$p.value
  ) %>%
  ungroup()

# 添加显著性标记
wilcox_results$significance <- ifelse(wilcox_results$wilcox_p_value < 0.001, "***",
                               ifelse(wilcox_results$wilcox_p_value < 0.01, "**",
                               ifelse(wilcox_results$wilcox_p_value < 0.05, "*", "ns")))

print("Wilcoxon检验结果:")
print(wilcox_results)
# 加载必要的包
library(ggplot2)
library(dplyr)

# 准备绘图数据
plot_data <- wilcox_results %>%
  mutate(
    # 为x轴创建数值型变量
    bin_numeric = Gene_bin,
    # 创建误差棒的上下限
    ymin = median_MLS_diff - se_MLS_diff,
    ymax = median_MLS_diff + se_MLS_diff
  )

# 创建精美的点图，带误差棒
p1 <- ggplot(plot_data, aes(x = bin_numeric, y = median_MLS_diff)) +
  # 添加误差棒
  geom_errorbar(aes(ymin = ymin, ymax = ymax), 
                width = 0.2, 
                size = 0.8, 
                color = "black") +
  # 添加数据点
  geom_point(size = 4, 
             color = "steelblue", 
             fill = "lightblue", 
             shape = 21, 
             stroke = 1.5) +
  # 添加水平参考线（y=0）
  geom_hline(yintercept = 0, 
             linetype = "dashed", 
             color = "red", 
             size = 0.8) +
  # 添加显著性标记
  geom_text(aes(label = significance), 
            vjust = -0.5, 
            hjust = 0.5, 
            size = 4, 
            fontface = "bold") +
  # 设置坐标轴
  scale_x_continuous(breaks = 1:5, 
                     labels = plot_data$aUTR_size_range) +
  # 设置主题和标签
  labs(
    title = "MLS差值按aUTR大小分组分析",
    subtitle = "MLS long isoform - MLS short isoform",
    x = "aUTR大小范围 (nt)",
    y = "MLS差值中位数",
    caption = paste("Wilcoxon单样本检验; *p<0.05, **p<0.01, ***p<0.001")
  ) +
  theme_classic() +
  theme(
    plot.title = element_text(size = 16, face = "bold", hjust = 0.5),
    plot.subtitle = element_text(size = 12, hjust = 0.5),
    axis.title = element_text(size = 12, face = "bold"),
    axis.text = element_text(size = 10),
    axis.text.x = element_text(angle = 45, hjust = 1),
    panel.grid.major.y = element_line(color = "grey90", size = 0.5),
    plot.caption = element_text(size = 9, color = "grey50")
  )

# 显示图表
print(p1)

# 保存图表
ggsave("aUTR_MLS_analysis_plot.png", plot = p1, 
       width = 10, height = 6, dpi = 300, bg = "white")
ggsave("aUTR_MLS_analysis_plot.pdf", plot = p1, 
       width = 10, height = 6, bg = "white")
# 创建最终的详细结果表
final_detailed_table <- wilcox_results %>%
  select(Gene_bin, aUTR_size_range, No_of_genes, 
         median_MLS_diff, se_MLS_diff, wilcox_p_value, significance) %>%
  mutate(
    wilcox_p_value = format(wilcox_p_value, scientific = TRUE, digits = 3)
  )

print("最终详细结果表:")
print(final_detailed_table)

# 保存所有结果
write.table(selected_data, "multi_polya_genes_complete_analysis.txt", sep = "\t", row.names = FALSE)
write.table(final_detailed_table, "aUTR_MLS_summary_with_stats.txt", sep = "\t", row.names = FALSE)
write.table(wilcox_results, "wilcoxon_test_results.txt", sep = "\t", row.names = FALSE)
